@file:Suppress("MaxLineLength", "LongParameterList")

package com.superhexa.supervision.feature.miwear.speechhub.service

import com.xiaomi.ai.capability.request.Phrase

/**
 * 转写状态监听接口
 * 允许UI组件注册监听转写状态变化，支持后台转写功能
 */
interface TranscriptionStateListener {

    /**
     * 转写任务开始
     * @param taskId 转写任务ID
     * @param filePath 音频文件路径
     */
    fun onTranscriptionStarted(taskId: String, filePath: String)

    /**
     * 转写成功完成
     * @param taskId 转写任务ID
     * @param filePath 音频文件路径
     * @param phrases 转写结果
     */
    fun onTranscriptionSuccess(taskId: String, filePath: String, phrases: List<Phrase>)

    /**
     * 转写失败
     * @param taskId 转写任务ID
     * @param filePath 音频文件路径
     * @param errorCode 错误码
     * @param errorMessage 错误信息
     */
    fun onTranscriptionFailed(taskId: String, filePath: String, errorCode: Int, errorMessage: String?)

    /**
     * 轮询状态变化
     * @param taskId 转写任务ID
     * @param filePath 音频文件路径
     * @param isPolling 是否正在轮询
     */
    fun onPollingStatusChanged(taskId: String, filePath: String, isPolling: Boolean)

    // ========== 录音上传相关回调方法 ==========

    /**
     * 录音上传开始
     * @param taskId 转写任务ID（此时为临时ID）
     * @param filePath 音频文件路径
     */
    fun onUploadStarted(taskId: String, filePath: String)

    /**
     * 录音上传成功
     * @param taskId 转写任务ID（云端返回的真实taskId）
     * @param filePath 音频文件路径
     */
    fun onUploadSuccess(taskId: String, filePath: String)

    /**
     * 录音上传失败
     * @param taskId 转写任务ID（此时为临时ID，因为未获得云端taskId）
     * @param filePath 音频文件路径
     * @param errorCode 错误码
     * @param errorMessage 错误信息
     */
    fun onUploadFailed(taskId: String, filePath: String, errorCode: Int, errorMessage: String?)

    // ========== 录音总结相关回调方法 ==========

    /**
     * 总结任务开始
     * @param taskId 转写任务ID
     * @param filePath 音频文件路径
     * @param summaryTaskId 总结任务ID
     * @param template 总结模板
     */
    fun onSummaryStarted(taskId: String, filePath: String, summaryTaskId: String, template: String)

    /**
     * 总结成功完成
     * @param taskId 转写任务ID
     * @param filePath 音频文件路径
     * @param summaryTaskId 总结任务ID
     * @param title 总结标题
     * @param content 总结内容
     * @param template 总结模板
     */
    fun onSummarySuccess(taskId: String, filePath: String, summaryTaskId: String, title: String, content: String, template: String)

    /**
     * 总结失败
     * @param taskId 转写任务ID
     * @param filePath 音频文件路径
     * @param summaryTaskId 总结任务ID
     * @param errorCode 错误码
     * @param template 总结模板
     */
    fun onSummaryFailed(taskId: String, filePath: String, summaryTaskId: String, errorCode: Int, template: String)
}
